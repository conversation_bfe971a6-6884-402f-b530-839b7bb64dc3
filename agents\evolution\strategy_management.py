#!/usr/bin/env python3
"""
Strategy Management Module for Options Strategy Evolution

This module handles strategy registry, lifecycle management, and population control.
It provides efficient storage and retrieval of strategies with performance-based management.
"""

import asyncio
import logging
import json
import yaml
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import aiofiles

logger = logging.getLogger(__name__)

class StrategyStatus(Enum):
    """Strategy status enumeration"""
    ACTIVE = "active"
    EXPERIMENTAL = "experimental"
    DEPRECATED = "deprecated"
    DISABLED = "disabled"
    PROMOTED = "promoted"
    DEMOTED = "demoted"

@dataclass
class StrategyConfig:
    """Strategy configuration structure"""
    strategy_id: str
    name: str
    description: str
    parameters: Dict[str, Any]
    entry_conditions: List[str]
    exit_conditions: List[str]
    risk_management: Dict[str, Any]
    market_outlook: str
    volatility_outlook: str
    timeframe: str
    status: StrategyStatus
    parent_id: Optional[str] = None
    version: str = "v1"
    created_at: Optional[datetime] = None
    tags: List[str] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.tags is None:
            self.tags = []
    
    def to_dict(self) -> Dict:
        """Convert to dictionary"""
        result = asdict(self)
        result['status'] = self.status.value
        result['created_at'] = self.created_at.isoformat()
        return result
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'StrategyConfig':
        """Create from dictionary"""
        data = data.copy()
        if 'status' in data:
            data['status'] = StrategyStatus(data['status'])
        if 'created_at' in data and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        return cls(**data)

class StrategyManager:
    """Manages strategy registry and lifecycle"""
    
    def __init__(self, performance_analyzer):
        self.performance_analyzer = performance_analyzer
        self.strategy_registry: Dict[str, StrategyConfig] = {}
        self.population_size = 100  # Default population size
        
        # File paths
        self.data_path = Path("data")
        self.strategies_path = self.data_path / "strategies"
        self.evolution_path = self.data_path / "strategy_evolution"
        self.registry_path = self.evolution_path / "registry"
        
        # Create directories
        for path in [self.strategies_path, self.evolution_path, self.registry_path]:
            path.mkdir(parents=True, exist_ok=True)
    
    async def initialize(self) -> bool:
        """Initialize strategy manager"""
        try:
            # Load existing strategies
            await self.load_strategy_registry()
            
            logger.info(f"[STRATEGY] Strategy manager initialized with {len(self.strategy_registry)} strategies")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Strategy manager initialization failed: {e}")
            return False
    
    async def load_strategy_registry(self):
        """Load strategy registry from disk and existing YAML file"""
        try:
            # First try to load from existing YAML file (for backward compatibility)
            await self._load_from_yaml_file()

            # Then load from JSON registry (newer format)
            registry_file = self.registry_path / "strategy_registry.json"

            if registry_file.exists():
                async with aiofiles.open(registry_file, 'r', encoding='utf-8') as f:
                    content = await f.read()
                    registry_data = json.loads(content)

                # Convert to StrategyConfig objects
                for strategy_id, strategy_data in registry_data.items():
                    try:
                        self.strategy_registry[strategy_id] = StrategyConfig.from_dict(strategy_data)
                    except Exception as e:
                        logger.warning(f"[STRATEGY] Failed to load strategy {strategy_id}: {e}")

                logger.info(f"[STRATEGY] Loaded {len(self.strategy_registry)} strategies from registry")
            else:
                logger.info("[STRATEGY] No existing registry found, starting with empty registry")

        except Exception as e:
            logger.error(f"[ERROR] Failed to load strategy registry: {e}")

    async def _load_from_yaml_file(self):
        """Load existing strategies from YAML file for backward compatibility"""
        try:
            yaml_file = Path("config/options_strategies.yaml")
            if yaml_file.exists():
                async with aiofiles.open(yaml_file, 'r', encoding='utf-8') as f:
                    content = await f.read()
                    yaml_data = yaml.safe_load(content)

                if yaml_data and 'strategies' in yaml_data:
                    strategies = yaml_data['strategies']
                    if isinstance(strategies, list):
                        # Convert list format to dict format
                        for strategy_data in strategies:
                            if isinstance(strategy_data, dict) and 'strategy_id' in strategy_data:
                                try:
                                    strategy = StrategyConfig.from_dict(strategy_data)
                                    self.strategy_registry[strategy.strategy_id] = strategy
                                except Exception as e:
                                    logger.warning(f"[STRATEGY] Failed to load YAML strategy: {e}")
                    elif isinstance(strategies, dict):
                        # Dict format
                        for strategy_id, strategy_data in strategies.items():
                            try:
                                if isinstance(strategy_data, dict):
                                    strategy_data['strategy_id'] = strategy_id
                                    strategy = StrategyConfig.from_dict(strategy_data)
                                    self.strategy_registry[strategy.strategy_id] = strategy
                            except Exception as e:
                                logger.warning(f"[STRATEGY] Failed to load YAML strategy {strategy_id}: {e}")

                    logger.info(f"[STRATEGY] Loaded {len(self.strategy_registry)} strategies from YAML file")

        except Exception as e:
            logger.debug(f"[STRATEGY] Could not load from YAML file: {e}")
    
    async def save_strategy_registry(self):
        """Save strategy registry to disk"""
        try:
            registry_file = self.registry_path / "strategy_registry.json"
            
            # Convert to serializable format
            registry_data = {}
            for strategy_id, strategy in self.strategy_registry.items():
                registry_data[strategy_id] = strategy.to_dict()
            
            async with aiofiles.open(registry_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(registry_data, indent=2, default=str))
            
            logger.debug(f"[STRATEGY] Saved {len(self.strategy_registry)} strategies to registry")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to save strategy registry: {e}")
    
    async def add_strategy(self, strategy: StrategyConfig) -> bool:
        """Add a strategy to the registry"""
        try:
            if strategy.strategy_id in self.strategy_registry:
                logger.warning(f"[STRATEGY] Strategy {strategy.strategy_id} already exists, updating")
            
            self.strategy_registry[strategy.strategy_id] = strategy
            
            # Save to disk
            await self.save_strategy_registry()
            
            logger.debug(f"[STRATEGY] Added strategy {strategy.strategy_id} to registry")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to add strategy {strategy.strategy_id}: {e}")
            return False
    
    async def remove_strategy(self, strategy_id: str) -> bool:
        """Remove a strategy from the registry"""
        try:
            if strategy_id not in self.strategy_registry:
                logger.warning(f"[STRATEGY] Strategy {strategy_id} not found in registry")
                return False
            
            del self.strategy_registry[strategy_id]
            
            # Remove from performance cache as well
            if strategy_id in self.performance_analyzer.performance_cache:
                del self.performance_analyzer.performance_cache[strategy_id]
            
            # Save to disk
            await self.save_strategy_registry()
            
            logger.info(f"[STRATEGY] Removed strategy {strategy_id} from registry")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to remove strategy {strategy_id}: {e}")
            return False
    
    async def get_strategy(self, strategy_id: str) -> Optional[StrategyConfig]:
        """Get a strategy by ID"""
        return self.strategy_registry.get(strategy_id)
    
    async def get_strategies_by_status(self, status: StrategyStatus) -> List[StrategyConfig]:
        """Get strategies by status"""
        return [strategy for strategy in self.strategy_registry.values() if strategy.status == status]
    
    async def get_active_strategies(self) -> List[StrategyConfig]:
        """Get all active strategies"""
        return await self.get_strategies_by_status(StrategyStatus.ACTIVE)
    
    async def get_experimental_strategies(self) -> List[StrategyConfig]:
        """Get all experimental strategies"""
        return await self.get_strategies_by_status(StrategyStatus.EXPERIMENTAL)
    
    async def control_population_size(self):
        """Control population size by removing worst performers"""
        try:
            if len(self.strategy_registry) <= self.population_size:
                return
            
            excess = len(self.strategy_registry) - self.population_size
            logger.info(f"[POPULATION] Population size {len(self.strategy_registry)} exceeds limit {self.population_size}, removing {excess} worst performers")
            
            # Get experimental strategies with their performance scores
            experimental_strategies = []
            
            for strategy_id, strategy in self.strategy_registry.items():
                if strategy.status != StrategyStatus.EXPERIMENTAL:
                    continue
                
                # Get performance score
                if strategy_id in self.performance_analyzer.performance_cache:
                    metrics = self.performance_analyzer.performance_cache[strategy_id]
                    experimental_strategies.append((strategy_id, metrics.composite_score))
                else:
                    # No performance data - mark for removal
                    experimental_strategies.append((strategy_id, -100.0))
            
            if not experimental_strategies:
                logger.warning("[POPULATION] No experimental strategies available for removal")
                return
            
            # Sort by performance score (ascending - worst first)
            experimental_strategies.sort(key=lambda x: x[1])
            
            # Remove worst performers
            removed_count = 0
            for strategy_id, score in experimental_strategies:
                if removed_count >= excess:
                    break
                
                await self.remove_strategy(strategy_id)
                logger.info(f"[POPULATION] Removed worst performer {strategy_id} (score: {score:.3f})")
                removed_count += 1
            
            logger.info(f"[POPULATION] Removed {removed_count} strategies, new population size: {len(self.strategy_registry)}")
            
        except Exception as e:
            logger.error(f"[ERROR] Population control failed: {e}")
            raise RuntimeError(f"Population control failed: {e}")
    
    async def promote_strategy(self, strategy_id: str) -> bool:
        """Promote a strategy from experimental to active"""
        try:
            strategy = await self.get_strategy(strategy_id)
            if not strategy:
                logger.error(f"[STRATEGY] Strategy {strategy_id} not found for promotion")
                return False
            
            strategy.status = StrategyStatus.PROMOTED
            await self.save_strategy_registry()
            
            logger.info(f"[STRATEGY] Promoted strategy {strategy_id} to active status")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to promote strategy {strategy_id}: {e}")
            return False
    
    async def demote_strategy(self, strategy_id: str) -> bool:
        """Demote a strategy from active to experimental"""
        try:
            strategy = await self.get_strategy(strategy_id)
            if not strategy:
                logger.error(f"[STRATEGY] Strategy {strategy_id} not found for demotion")
                return False
            
            strategy.status = StrategyStatus.DEMOTED
            await self.save_strategy_registry()
            
            logger.info(f"[STRATEGY] Demoted strategy {strategy_id} to experimental status")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to demote strategy {strategy_id}: {e}")
            return False
    
    async def export_strategies_to_yaml(self, output_path: str = "config/options_strategies.yaml"):
        """Export strategies to YAML file for use by other systems"""
        try:
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Get all strategies (including experimental ones for evolution)
            strategies_to_export = []
            for strategy in self.strategy_registry.values():
                # Export all strategies, not just active/promoted ones
                # This allows the system to use experimental strategies for further evolution
                strategies_to_export.append(strategy.to_dict())
            
            # Export to YAML
            async with aiofiles.open(output_file, 'w', encoding='utf-8') as f:
                yaml_content = yaml.dump({
                    'strategies': strategies_to_export,
                    'exported_at': datetime.now().isoformat(),
                    'total_strategies': len(strategies_to_export)
                }, default_flow_style=False, indent=2)
                await f.write(yaml_content)
            
            logger.info(f"[EXPORT] Exported {len(strategies_to_export)} strategies to {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to export strategies: {e}")
            return False
    
    def get_registry_summary(self) -> Dict[str, Any]:
        """Get summary of strategy registry"""
        try:
            status_counts = {}
            for status in StrategyStatus:
                status_counts[status.value] = sum(1 for s in self.strategy_registry.values() if s.status == status)
            
            return {
                'total_strategies': len(self.strategy_registry),
                'status_breakdown': status_counts,
                'population_limit': self.population_size,
                'population_usage': f"{len(self.strategy_registry)}/{self.population_size}",
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get registry summary: {e}")
            return {'error': str(e)}
