"""
Reporting and results saving for the backtesting engine.
"""
import json
import logging
from dataclasses import asdict
from datetime import datetime
from pathlib import Path
from typing import Dict, List

import aiofiles
import polars as pl

from .config import BacktestResults

logger = logging.getLogger(__name__)

RESULTS_PATH = Path("data/results")
AI_TRAINING_PATH = Path("data/ai_training")


async def generate_backtest_report(backtest_results: Dict[str, BacktestResults]):
    """Generate and save a comprehensive backtest report."""
    try:
        logger.info("Generating backtest report...")
        if not backtest_results:
            logger.warning("No backtest results to report")
            return

        results_data = []
        for result in backtest_results.values():
            result_dict = asdict(result)
            if not result_dict.get("trades"):
                result_dict["trades"] = None
            results_data.append(result_dict)
        
        results_df = pl.DataFrame(results_data)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        RESULTS_PATH.mkdir(parents=True, exist_ok=True)
        results_file = RESULTS_PATH / f"backtest_results_{timestamp}.parquet"
        results_df.write_parquet(results_file)

        summary = {
            'total_strategies': len(backtest_results),
            'profitable_strategies': len([r for r in backtest_results.values() if r.total_return > 0]),
            'avg_return': results_df['total_return'].mean(),
            'avg_sharpe': results_df['sharpe_ratio'].mean(),
            'avg_win_rate': results_df['win_rate'].mean(),
            'best_strategy': results_df.sort('total_return', descending=True).row(0, named=True)['strategy_id'],
            'worst_strategy': results_df.sort('total_return').row(0, named=True)['strategy_id']
        }

        summary_file = RESULTS_PATH / f"backtest_summary_{timestamp}.json"
        async with aiofiles.open(summary_file, 'w') as f:
            await f.write(json.dumps(summary, indent=2, default=str))

        logger.info(f"Backtest report generated: {len(backtest_results)} strategies")

    except Exception as e:
        logger.error(f"Failed to generate backtest report: {e}")


async def save_results_for_ai_training(backtest_results: Dict[str, BacktestResults]):
    """Save backtest results in a format suitable for AI training."""
    try:
        logger.info("Saving results for AI training...")
        if not backtest_results:
            logger.warning("No backtest results to save for AI training")
            return

        training_data = []
        for result in backtest_results.values():
            result_dict = asdict(result)
            if not result_dict.get("trades"):
                result_dict["trades"] = None
            training_data.append(result_dict)
        
        training_df = pl.DataFrame(training_data)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        AI_TRAINING_PATH.mkdir(parents=True, exist_ok=True)
        
        training_file = AI_TRAINING_PATH / f"strategy_performance_{timestamp}.parquet"
        training_df.write_parquet(training_file)

        logger.info(f"Saved {len(training_data)} strategy records for AI training to {training_file}")

    except Exception as e:
        logger.error(f"Failed to save AI training data: {e}")
