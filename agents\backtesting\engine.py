"""
High-performance backtesting engine using vectorbt, numba, and numexpr.
"""
import logging
import multiprocessing as mp
import traceback
from concurrent.futures import ProcessPoolExecutor, as_completed
from typing import Dict, List, Optional

import numpy as np
import polars as pl
import vectorbt as vbt

from .calculations import (
    calculate_max_drawdown_numba,
    calculate_returns_numba,
    calculate_rolling_mean_numba,
    calculate_rolling_std_numba,
    optimize_with_numexpr,
)
from .config import BacktestConfig, BacktestResults

logger = logging.getLogger(__name__)

VBT_AVAILABLE = True
try:
    vbt.settings.caching['enabled'] = True
    vbt.settings.caching['whitelist'] = []
    vbt.settings.array_wrapper['freq'] = '1min'
    logging.info("VectorBT loaded successfully")
except ImportError:
    VBT_AVAILABLE = False
    logging.warning("vectorbt not installed. Install with: pip install vectorbt")


class HighPerformanceBacktestEngine:
    """
    High-performance backtesting engine.
    """

    def __init__(self, risk_free_rate: float = 0.06):
        self.risk_free_rate = risk_free_rate
        self.use_vectorbt = VBT_AVAILABLE

    def run_backtest(
        self, data: pl.DataFrame, strategies: List[Dict], config: BacktestConfig
    ) -> Dict[str, BacktestResults]:
        """Run vectorized backtesting for multiple strategies."""
        try:
            logger.info(f"Starting backtest for {len(strategies)} strategies")
            if self.use_vectorbt:
                return self._vectorbt_backtest(data, strategies, config)
            else:
                return self._parallel_backtest(data, strategies, config)
        except Exception as e:
            logger.error(f"Backtest failed: {e}")
            logger.error(traceback.format_exc())
            return {}

    def _vectorbt_backtest(
        self, data: pl.DataFrame, strategies: List[Dict], config: BacktestConfig
    ) -> Dict[str, BacktestResults]:
        """Ultra-fast backtesting using VectorBT."""
        close_prices = data["close"].to_numpy()
        results = {}

        for strategy in strategies:
            try:
                entries = self._generate_signals(data, strategy)
                exits = self._generate_signals(data, strategy, is_entry=False)

                if entries.sum() == 0:
                    results[strategy['strategy_id']] = self._create_empty_results(strategy['strategy_id'])
                    continue

                portfolio = vbt.Portfolio.from_signals(
                    close=close_prices,
                    entries=entries,
                    exits=exits,
                    init_cash=config.initial_capital,
                    fees=config.transaction_cost,
                    slippage=config.slippage,
                    freq="1T",
                )
                results[strategy['strategy_id']] = self._extract_vbt_results(
                    portfolio, strategy['strategy_id']
                )
            except Exception as e:
                logger.error(f"VectorBT backtest for strategy {strategy['strategy_id']} failed: {e}")
                results[strategy['strategy_id']] = self._create_empty_results(strategy['strategy_id'])
        
        return results

    def _generate_signals(self, data: pl.DataFrame, strategy: Dict, is_entry: bool = True) -> np.ndarray:
        """Generate entry or exit signals based on strategy conditions."""
        conditions = strategy.get('entry_conditions' if is_entry else 'exit_conditions', [])
        if not conditions:
            return np.zeros(len(data), dtype=bool)

        final_signal = np.ones(len(data), dtype=bool)
        for condition in conditions:
            # This is a simplified signal generation logic.
            # A more robust implementation would parse and apply each condition properly.
            if 'volatility' in condition:
                vol = data['close'].rolling_std(20).to_numpy()
                threshold = float(condition.split('>')[-1].strip())
                final_signal &= (vol > threshold)
            elif 'volume' in condition:
                vol_sma = data['volume'].rolling_mean(20).to_numpy()
                final_signal &= (data['volume'].to_numpy() > vol_sma)

        return final_signal

    def _extract_vbt_results(self, portfolio, strategy_id: str) -> BacktestResults:
        """Extract results from a VectorBT portfolio."""
        stats = portfolio.stats()
        return BacktestResults(
            strategy_id=strategy_id,
            total_return=stats['Total Return [%]'] / 100,
            annualized_return=stats.get('Annualized Return [%]', 0) / 100,
            sharpe_ratio=stats.get('Sharpe Ratio', 0),
            sortino_ratio=stats.get('Sortino Ratio', 0),
            max_drawdown=stats['Max Drawdown [%]'] / 100,
            win_rate=stats['Win Rate [%]'] / 100,
            profit_factor=stats.get('Profit Factor', 0),
            total_trades=stats['Total Trades'],
            avg_trade_return=stats.get('Avg Trade [%]', 0) / 100,
            best_trade=stats.get('Best Trade [%]', 0) / 100,
            worst_trade=stats.get('Worst Trade [%]', 0) / 100,
            avg_holding_period=stats.get('Avg Holding Duration', 0),
            greeks_pnl={},
            volatility_pnl=0,
            time_decay_pnl=0,
        )

    def _parallel_backtest(
        self, data: pl.DataFrame, strategies: List[Dict], config: BacktestConfig
    ) -> Dict[str, BacktestResults]:
        """Parallel backtesting for non-VectorBT fallback."""
        results = {}
        prices = data["close"].to_numpy()
        
        with ProcessPoolExecutor(max_workers=mp.cpu_count()) as executor:
            future_to_strategy = {
                executor.submit(self._run_single_backtest, strategy, prices, config): strategy
                for strategy in strategies
            }
            for future in as_completed(future_to_strategy):
                strategy = future_to_strategy[future]
                try:
                    result = future.result()
                    results[strategy['strategy_id']] = result
                except Exception as e:
                    logger.error(f"Parallel backtest for strategy {strategy['strategy_id']} failed: {e}")
                    results[strategy['strategy_id']] = self._create_empty_results(strategy['strategy_id'])
        return results

    def _run_single_backtest(self, strategy: Dict, prices: np.ndarray, config: BacktestConfig) -> BacktestResults:
        """Run a single backtest simulation."""
        # Simplified simulation logic
        equity_curve = np.full(len(prices), config.initial_capital)
        # In a real scenario, you would generate signals and simulate trades here.
        
        total_return = (equity_curve[-1] - config.initial_capital) / config.initial_capital
        returns = calculate_returns_numba(equity_curve)
        sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
        max_drawdown = calculate_max_drawdown_numba(equity_curve)

        return BacktestResults(
            strategy_id=strategy['strategy_id'],
            total_return=total_return,
            annualized_return=0, # Simplified
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=0, # Simplified
            max_drawdown=max_drawdown,
            win_rate=0, # Simplified
            profit_factor=0, # Simplified
            total_trades=0, # Simplified
            avg_trade_return=0,
            best_trade=0,
            worst_trade=0,
            avg_holding_period=0,
            greeks_pnl={},
            volatility_pnl=0,
            time_decay_pnl=0,
        )

    def _create_empty_results(self, strategy_id: str) -> BacktestResults:
        """Create empty results for a failed or non-trading strategy."""
        return BacktestResults(
            strategy_id=strategy_id,
            total_return=0.0,
            annualized_return=0.0,
            sharpe_ratio=0.0,
            sortino_ratio=0.0,
            max_drawdown=0.0,
            win_rate=0.0,
            profit_factor=0.0,
            total_trades=0,
            avg_trade_return=0.0,
            best_trade=0.0,
            worst_trade=0.0,
            avg_holding_period=0.0,
            greeks_pnl={},
            volatility_pnl=0.0,
            time_decay_pnl=0.0,
        )
