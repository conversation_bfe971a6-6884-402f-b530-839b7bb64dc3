WARNING:agents.evolution.performance_analysis:[IMPORT] Performance calculation functions not available
2025-08-17 14:19:02,081 - __main__ - INFO - [INIT] Options System Orchestrator initialized
2025-08-17 14:19:02,082 - __main__ - INFO - [REAL] Real trading mode enabled - SmartAPI integration active
2025-08-17 14:19:02,082 - __main__ - INFO - [PIPELINE] Backtesting requires feature engineered data. Running feature engineering first...
2025-08-17 14:19:02,082 - __main__ - INFO - [AGENT] Starting feature_engineering agent in REAL mode...
2025-08-17 14:19:02,084 - agents.options_feature_engineering_agent - INFO - [INIT] Options Feature Engineering Agent initialized
2025-08-17 14:19:02,084 - agents.options_feature_engineering_agent - INFO - [CONFIG] Configuration loaded successfully
2025-08-17 14:19:02,084 - agents.options_feature_engineering_agent - INFO - [SUCCESS] Options Feature Engineering Agent initialized successfully
2025-08-17 14:19:02,084 - agents.options_feature_engineering_agent - INFO - [START] Starting Enhanced Options Feature Engineering Agent...
2025-08-17 14:19:02,085 - agents.options_feature_engineering_agent - INFO - [MODE] Live mode - processing real-time features
2025-08-17 14:19:02,086 - agents.options_feature_engineering_agent - INFO - [PROCESS] Processing 115 1min files for BANKNIFTY...
2025-08-17 14:19:02,493 - agents.options_feature_engineering_agent - INFO - [SAVE] 1min features saved for BANKNIFTY: 187003 records
2025-08-17 14:19:02,494 - agents.options_feature_engineering_agent - INFO - [SUCCESS] 1min features processed for BANKNIFTY: 187003 total records
2025-08-17 14:19:02,496 - agents.options_feature_engineering_agent - INFO - [PROCESS] Processing 59 1min files for NIFTY...
2025-08-17 14:19:02,694 - agents.options_feature_engineering_agent - INFO - [SAVE] 1min features saved for NIFTY: 99588 records
2025-08-17 14:19:02,694 - agents.options_feature_engineering_agent - INFO - [SUCCESS] 1min features processed for NIFTY: 99588 total records
2025-08-17 14:19:02,697 - agents.options_feature_engineering_agent - INFO - [PROCESS] Processing 115 3min files for BANKNIFTY...
2025-08-17 14:19:02,996 - agents.options_feature_engineering_agent - INFO - [SAVE] 3min features saved for BANKNIFTY: 66307 records
2025-08-17 14:19:02,997 - agents.options_feature_engineering_agent - INFO - [SUCCESS] 3min features processed for BANKNIFTY: 66307 total records
2025-08-17 14:19:02,999 - agents.options_feature_engineering_agent - INFO - [PROCESS] Processing 59 3min files for NIFTY...
2025-08-17 14:19:03,162 - agents.options_feature_engineering_agent - INFO - [SAVE] 3min features saved for NIFTY: 34881 records
2025-08-17 14:19:03,162 - agents.options_feature_engineering_agent - INFO - [SUCCESS] 3min features processed for NIFTY: 34881 total records
2025-08-17 14:19:03,165 - agents.options_feature_engineering_agent - INFO - [PROCESS] Processing 115 5min files for BANKNIFTY...
2025-08-17 14:19:03,451 - agents.options_feature_engineering_agent - INFO - [SAVE] 5min features saved for BANKNIFTY: 40649 records
2025-08-17 14:19:03,451 - agents.options_feature_engineering_agent - INFO - [SUCCESS] 5min features processed for BANKNIFTY: 40649 total records
2025-08-17 14:19:03,453 - agents.options_feature_engineering_agent - INFO - [PROCESS] Processing 59 5min files for NIFTY...
2025-08-17 14:19:03,614 - agents.options_feature_engineering_agent - INFO - [SAVE] 5min features saved for NIFTY: 21333 records
2025-08-17 14:19:03,615 - agents.options_feature_engineering_agent - INFO - [SUCCESS] 5min features processed for NIFTY: 21333 total records
2025-08-17 14:19:03,617 - agents.options_feature_engineering_agent - INFO - [PROCESS] Processing 115 15min files for BANKNIFTY...
2025-08-17 14:19:03,885 - agents.options_feature_engineering_agent - INFO - [SAVE] 15min features saved for BANKNIFTY: 13988 records
2025-08-17 14:19:03,886 - agents.options_feature_engineering_agent - INFO - [SUCCESS] 15min features processed for BANKNIFTY: 13988 total records
2025-08-17 14:19:03,888 - agents.options_feature_engineering_agent - INFO - [PROCESS] Processing 59 15min files for NIFTY...
2025-08-17 14:19:04,037 - agents.options_feature_engineering_agent - INFO - [SAVE] 15min features saved for NIFTY: 7292 records
2025-08-17 14:19:04,037 - agents.options_feature_engineering_agent - INFO - [SUCCESS] 15min features processed for NIFTY: 7292 total records
2025-08-17 14:19:04,038 - agents.options_feature_engineering_agent - INFO - [SUCCESS] Multi-timeframe feature engineering completed
2025-08-17 14:19:04,038 - __main__ - INFO - [SUCCESS] feature_engineering agent started successfully
2025-08-17 14:19:04,038 - __main__ - INFO - [AGENT] Running backtesting agent...
2025-08-17 14:19:04,039 - __main__ - INFO - [AGENT] Starting backtesting agent in REAL mode...
2025-08-17 14:19:04,039 - __main__ - INFO - [AGENT] Backtesting agent requires strategies from YAML. Loading...
2025-08-17 14:19:04,052 - __main__ - INFO - [YAML] Loaded strategies configuration from config\options_strategies.yaml
2025-08-17 14:19:04,052 - __main__ - INFO - [YAML] Loaded strategy: Basic Long Straddle (straddle_basic_20250817_124758)
2025-08-17 14:19:04,052 - __main__ - INFO - [YAML] Loaded strategy: Basic Long Strangle (strangle_basic_20250817_124758)
2025-08-17 14:19:04,053 - __main__ - INFO - [YAML] Loaded strategy: Basic Iron Condor (iron_condor_basic_20250817_124758)
2025-08-17 14:19:04,053 - __main__ - INFO - [YAML] Loaded strategy: Basic Covered Call (covered_call_basic_20250817_124758)
2025-08-17 14:19:04,053 - __main__ - INFO - [YAML] Loaded strategy: Basic Protective Put (protective_put_basic_20250817_124758)
2025-08-17 14:19:04,053 - __main__ - INFO - [YAML] Loaded strategy: Crossover Basic Long Straddle x Basic Long Strangle (crossover_5a196d6b_0)
2025-08-17 14:19:04,056 - __main__ - INFO - [SUCCESS] Loaded 6 strategies from YAML and saved to data\strategies\strategies_from_yaml_20250817_141904.parquet 
2025-08-17 14:19:04,056 - agents.backtesting_agent - INFO - Configuration loaded successfully
2025-08-17 14:19:04,056 - agents.backtesting_agent - INFO - Options Backtesting Agent initialized successfully
2025-08-17 14:19:04,057 - agents.backtesting_agent - INFO - Starting Options Backtesting Agent...
2025-08-17 14:19:04,057 - agents.backtesting.data_loader - INFO - Loading latest generated strategies...
2025-08-17 14:19:04,061 - agents.backtesting.data_loader - INFO - Loaded 91 strategies from generated_strategies_20250718_192943.json
2025-08-17 14:19:04,061 - agents.backtesting.data_loader - INFO - Loading feature-engineered data...
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Option\agents\backtesting\data_loader.py", line 114, in load_feature_engineered_data
    date_filtered = combined_data.filter(
                    ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\polars\dataframe\frame.py", line 5181, in filter
    .collect(optimizations=QueryOptFlags._eager())
     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\polars\_utils\deprecation.py", line 97, in wrapper
    return function(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\polars\lazyframe\opt_flags.py", line 330, in wrapper
    return function(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\polars\lazyframe\frame.py", line 2332, in collect
    return wrap_df(ldf.collect(engine, callback))
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
polars.exceptions.SchemaError: could not evaluate '>=' comparison between series 'timestamp' of dtype: datetime[μs, UTC] and series 'literal' of dtype: datetime[μs]

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\ProgramData\anaconda3\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\ProgramData\anaconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u03bc' in position 188: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\Option\main.py", line 1056, in <module>
    asyncio.run(main())
  File "C:\ProgramData\anaconda3\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
  File "C:\ProgramData\anaconda3\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\ProgramData\anaconda3\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
  File "C:\ProgramData\anaconda3\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\ProgramData\anaconda3\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
  File "C:\ProgramData\anaconda3\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
  File "C:\ProgramData\anaconda3\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\Option\main.py", line 1007, in main
    success = await orchestrator.run_agent(
  File "C:\Users\<USER>\Documents\Option\main.py", line 270, in run_agent
    success = await agent.start(**agent_kwargs)
  File "C:\Users\<USER>\Documents\Option\agents\backtesting_agent.py", line 64, in start
    feature_data = await load_feature_engineered_data(
  File "C:\Users\<USER>\Documents\Option\agents\backtesting\data_loader.py", line 121, in load_feature_engineered_data
    logger.error(f"Failed to load feature-engineered data: {e}")
Message: "Failed to load feature-engineered data: could not evaluate '>=' comparison between series 'timestamp' of dtype: datetime[μs, UTC] and series 'literal' of dtype: datetime[μs]"
Arguments: ()
2025-08-17 14:19:05,126 - agents.backtesting.data_loader - ERROR - Failed to load feature-engineered data: could not evaluate '>=' comparison between series 'timestamp' of dtype: datetime[μs, UTC] and series 'literal' of dtype: datetime[μs]
2025-08-17 14:19:05,144 - agents.backtesting_agent - WARNING - No feature-engineered data found for the specified date range
2025-08-17 14:19:05,144 - __main__ - ERROR - [ERROR] backtesting agent failed to start
[ERROR] backtesting agent failed
2025-08-17 14:19:05,144 - __main__ - INFO - [CLEANUP] Shutting down all agents...
2025-08-17 14:19:05,144 - agents.options_feature_engineering_agent - INFO - [CLEANUP] Cleaning up Options Feature Engineering Agent...
2025-08-17 14:19:05,145 - agents.options_feature_engineering_agent - INFO - [SUCCESS] Options Feature Engineering Agent cleaned up
2025-08-17 14:19:05,145 - __main__ - INFO - [CLEANUP] feature_engineering agent cleaned up
2025-08-17 14:19:05,145 - agents.backtesting_agent - INFO - Cleaning up Options Backtesting Agent...
2025-08-17 14:19:05,145 - __main__ - INFO - [CLEANUP] backtesting agent cleaned up
2025-08-17 14:19:05,146 - __main__ - INFO - [CLEANUP] All agents cleaned up successfully
(base) PS C:\Users\<USER>\Documents\Option> 