{"straddle_basic_20250817_124758": {"strategy_id": "straddle_basic_20250817_124758", "name": "Basic Long Straddle", "description": "Basic long straddle strategy for high volatility markets", "parameters": {"strike_selection": "ATM", "expiry_days": 30, "iv_rank_min": 50, "iv_rank_max": 100}, "entry_conditions": ["volatility_20d > 0.15", "volume > volume_sma_20", "hour >= 9 and hour <= 15"], "exit_conditions": ["profit_target >= 0.5", "stop_loss <= -0.3", "hour == 15"], "risk_management": {"stop_loss": 0.3, "take_profit": 0.5, "position_size": 0.02}, "market_outlook": "neutral", "volatility_outlook": "high", "timeframe": "5min", "status": "experimental", "parent_id": null, "version": "v1", "created_at": "2025-08-17T12:47:58.973421", "tags": ["straddle", "volatility", "initial"]}, "strangle_basic_20250817_124758": {"strategy_id": "strangle_basic_20250817_124758", "name": "Basic Long Strangle", "description": "Basic long strangle strategy for moderate volatility", "parameters": {"call_strike_delta": 0.25, "put_strike_delta": -0.25, "expiry_days": 45, "iv_rank_min": 30}, "entry_conditions": ["volatility_20d > 0.10", "volume > volume_sma_20 * 1.2", "hour >= 10 and hour <= 14"], "exit_conditions": ["profit_target >= 0.4", "stop_loss <= -0.4", "hour == 15"], "risk_management": {"stop_loss": 0.4, "take_profit": 0.4, "position_size": 0.03}, "market_outlook": "neutral", "volatility_outlook": "normal", "timeframe": "15min", "status": "experimental", "parent_id": null, "version": "v1", "created_at": "2025-08-17T12:47:58.974423", "tags": ["strangle", "volatility", "initial"]}, "iron_condor_basic_20250817_124758": {"strategy_id": "iron_condor_basic_20250817_124758", "name": "Basic Iron Condor", "description": "Basic iron condor for range-bound markets", "parameters": {"wing_width": 100, "short_strike_delta": 0.15, "expiry_days": 30, "iv_rank_max": 70}, "entry_conditions": ["volatility_20d < 0.20", "volume > volume_sma_20 * 0.8", "hour >= 10 and hour <= 13"], "exit_conditions": ["profit_target >= 0.25", "stop_loss <= -0.5", "hour >= 14"], "risk_management": {"stop_loss": 0.5, "take_profit": 0.25, "position_size": 0.05}, "market_outlook": "neutral", "volatility_outlook": "low", "timeframe": "5min", "status": "experimental", "parent_id": null, "version": "v1", "created_at": "2025-08-17T12:47:58.975421", "tags": ["iron_condor", "range_bound", "initial"]}, "covered_call_basic_20250817_124758": {"strategy_id": "covered_call_basic_20250817_124758", "name": "Basic Covered Call", "description": "Basic covered call for income generation", "parameters": {"call_strike_delta": 0.3, "expiry_days": 30, "underlying_position": "long"}, "entry_conditions": ["returns_pct > 0.01", "volume > volume_sma_20", "hour >= 9 and hour <= 12"], "exit_conditions": ["profit_target >= 0.2", "stop_loss <= -0.1", "hour >= 14"], "risk_management": {"stop_loss": 0.1, "take_profit": 0.2, "position_size": 0.1}, "market_outlook": "bullish", "volatility_outlook": "normal", "timeframe": "15min", "status": "experimental", "parent_id": null, "version": "v1", "created_at": "2025-08-17T12:47:58.977421", "tags": ["covered_call", "income", "initial"]}, "protective_put_basic_20250817_124758": {"strategy_id": "protective_put_basic_20250817_124758", "name": "Basic Protective Put", "description": "Basic protective put for downside protection", "parameters": {"put_strike_delta": -0.2, "expiry_days": 60, "underlying_position": "long"}, "entry_conditions": ["volatility_20d > 0.20", "returns_pct < -0.01", "hour >= 9 and hour <= 13"], "exit_conditions": ["profit_target >= 0.15", "stop_loss <= -0.05", "hour >= 14"], "risk_management": {"stop_loss": 0.05, "take_profit": 0.15, "position_size": 0.08}, "market_outlook": "bearish", "volatility_outlook": "high", "timeframe": "5min", "status": "experimental", "parent_id": null, "version": "v1", "created_at": "2025-08-17T12:47:58.978420", "tags": ["protective_put", "hedge", "initial"]}, "crossover_5a196d6b_0": {"strategy_id": "crossover_5a196d6b_0", "name": "Crossover Basic Long Straddle x Basic Long Strangle", "description": "Crossover of Basic Long Straddle and Basic Long Strangle", "parameters": {"strike_selection": "ATM", "expiry_days": 45, "iv_rank_min": 30, "iv_rank_max": 100, "call_strike_delta": 0.25, "put_strike_delta": -0.25}, "entry_conditions": ["volatility_20d > 0.10", "volatility_20d > 0.15", "volume > volume_sma_20", "volume > volume_sma_20 * 1.2", "hour >= 10 and hour <= 14", "hour >= 9 and hour <= 15"], "exit_conditions": ["profit_target >= 0.5", "profit_target >= 0.4", "hour == 15", "stop_loss <= -0.3", "stop_loss <= -0.4"], "risk_management": {"stop_loss": 0.35, "take_profit": 0.45, "position_size": 0.025}, "market_outlook": "neutral", "volatility_outlook": "high", "timeframe": "5min", "status": "experimental", "parent_id": "straddle_basic_20250817_124758+strangle_basic_20250817_124758", "version": "v1", "created_at": "2025-08-17T13:21:21.174874", "tags": ["straddle", "performance_based_crossover", "crossover_0", "initial", "strangle", "volatility"]}}