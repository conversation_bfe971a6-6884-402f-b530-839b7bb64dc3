#!/usr/bin/env python3
"""
Data Integration Manager for Options Strategy Evolution

This module efficiently manages data loading and backtesting integration for strategy evolution.
It uses existing data from data/features/ directories and integrates directly with the backtesting agent
instead of downloading data from scratch.
"""

import asyncio
import logging
import polars as pl
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class BacktestConfig:
    """Configuration for backtesting"""
    timeframe: str = "5min"
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    symbols: List[str] = None
    max_trades: int = 1000
    
    def __post_init__(self):
        if self.symbols is None:
            self.symbols = ['NIFTY', 'BANKNIFTY']
        if self.start_date is None:
            self.start_date = datetime.now() - timedelta(days=30)
        if self.end_date is None:
            self.end_date = datetime.now()

class DataIntegrationManager:
    """Manages efficient data loading and backtesting for strategy evolution"""
    
    def __init__(self):
        self.data_path = Path("data")
        self.features_path = self.data_path / "features"
        self.cached_data: Dict[str, pl.DataFrame] = {}
        self.backtesting_agent = None
        
    async def initialize(self):
        """Initialize the data integration manager"""
        try:
            # Import and initialize backtesting agent
            from agents.options_backtesting_agent import OptionsBacktestingAgent
            self.backtesting_agent = OptionsBacktestingAgent()
            await self.backtesting_agent.initialize()
            
            logger.info("[DATA] Data integration manager initialized")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize data integration manager: {e}")
            return False
    
    async def load_features_data(self, timeframe: str = "5min", symbols: List[str] = None) -> Optional[pl.DataFrame]:
        """Load features data from existing data/features/ directory"""
        try:
            if symbols is None:
                symbols = ['NIFTY', 'BANKNIFTY']
            
            cache_key = f"{timeframe}_{'-'.join(symbols)}"
            if cache_key in self.cached_data:
                logger.debug(f"[DATA] Using cached data for {cache_key}")
                return self.cached_data[cache_key]
            
            features_timeframe_path = self.features_path / timeframe
            if not features_timeframe_path.exists():
                logger.warning(f"[DATA] Features path not found: {features_timeframe_path}")
                return None
            
            # Load all parquet files for the timeframe
            parquet_files = list(features_timeframe_path.glob("*.parquet"))
            if not parquet_files:
                logger.warning(f"[DATA] No parquet files found in {features_timeframe_path}")
                return None
            
            # Load and combine data
            dataframes = []
            for file in parquet_files:
                try:
                    df = pl.read_parquet(file)
                    # Filter for requested symbols if symbol column exists
                    if 'symbol' in df.columns:
                        df = df.filter(pl.col('symbol').is_in(symbols))
                    elif 'underlying' in df.columns:
                        df = df.filter(pl.col('underlying').is_in(symbols))
                    
                    if df.height > 0:
                        dataframes.append(df)
                        
                except Exception as e:
                    logger.warning(f"[DATA] Failed to load {file}: {e}")
                    continue
            
            if not dataframes:
                logger.warning(f"[DATA] No valid data loaded for {timeframe}")
                return None
            
            # Ensure consistent column ordering before concatenation
            if dataframes:
                # Get the column order from the first dataframe
                reference_columns = dataframes[0].columns

                # Reorder all dataframes to match the reference column order
                aligned_dataframes = []
                for df in dataframes:
                    # Only reorder if columns match (same set, potentially different order)
                    if set(df.columns) == set(reference_columns):
                        df_aligned = df.select(reference_columns)
                        aligned_dataframes.append(df_aligned)
                    else:
                        logger.warning(f"[DATA] Column mismatch detected, skipping dataframe with columns: {df.columns}")

                if not aligned_dataframes:
                    logger.warning(f"[DATA] No dataframes with consistent columns for {timeframe}")
                    return None

                # Combine all aligned dataframes
                combined_df = pl.concat(aligned_dataframes)

                # Sort by timestamp if available
                if 'timestamp' in combined_df.columns:
                    combined_df = combined_df.sort('timestamp')
            else:
                logger.warning(f"[DATA] No dataframes to combine for {timeframe}")
                return None
            
            # Cache the data
            self.cached_data[cache_key] = combined_df
            
            logger.info(f"[DATA] Loaded {combined_df.height} rows of features data for {timeframe}")
            return combined_df
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load features data: {e}")
            return None
    
    async def run_strategy_backtest(self, strategy_config: Dict, config: BacktestConfig) -> Optional[Dict]:
        """Run backtest using existing backtesting agent with loaded data"""
        try:
            if not self.backtesting_agent:
                raise ValueError("Backtesting agent not initialized")

            # Load features data for the specified timeframe
            features_data = await self.load_features_data(config.timeframe, config.symbols)
            if features_data is None:
                raise ValueError(f"No features data available for {config.timeframe}")

            # Filter data by date range
            if 'timestamp' in features_data.columns:
                # Ensure timezone compatibility for comparison
                start_date = config.start_date
                end_date = config.end_date

                # If the timestamp column has timezone info but our dates don't, add UTC timezone
                if start_date.tzinfo is None:
                    import pytz
                    start_date = pytz.UTC.localize(start_date)
                    end_date = pytz.UTC.localize(end_date)

                features_data = features_data.filter(
                    (pl.col('timestamp') >= start_date) &
                    (pl.col('timestamp') <= end_date)
                )

            if features_data.height == 0:
                raise ValueError("No data available for specified date range")

            # Convert strategy config to the format expected by backtesting agent
            backtest_strategy = self._convert_strategy_config(strategy_config)

            # Prepare market data in the format expected by backtesting agent
            market_data = self._prepare_market_data_for_backtest(features_data, config)

            # Run backtest using the backtesting agent directly with optimized parameters
            backtest_results = await self._run_optimized_backtest(
                backtest_strategy, market_data, config
            )

            if not backtest_results:
                raise ValueError("Backtesting failed to produce results")

            logger.debug(f"[BACKTEST] Completed backtest for strategy {strategy_config.get('strategy_id', 'unknown')}")
            return backtest_results

        except Exception as e:
            logger.error(f"[ERROR] Strategy backtest failed: {e}")
            return None

    async def _run_optimized_backtest(self, strategy, market_data: Dict, config: BacktestConfig) -> Optional[Dict]:
        """Run optimized backtest with different parameter variations"""
        try:
            # Test multiple parameter variations for better evolution
            parameter_variations = self._generate_parameter_variations(strategy, config)

            best_result = None
            best_score = -float('inf')

            for variation in parameter_variations:
                try:
                    # Run backtest with parameter variation
                    result = await self.backtesting_agent.run_strategy_backtest(
                        strategy=variation,
                        market_data=market_data,
                        start_date=config.start_date,
                        end_date=config.end_date
                    )

                    if result and 'trades' in result:
                        # Calculate quick score for this variation
                        trades = result['trades']
                        if trades:
                            returns = [trade.get('return', 0.0) for trade in trades]
                            score = self._calculate_quick_score(returns)

                            if score > best_score:
                                best_score = score
                                best_result = result
                                best_result['parameter_variation'] = variation.to_dict() if hasattr(variation, 'to_dict') else str(variation)

                except Exception as e:
                    logger.debug(f"[BACKTEST] Parameter variation failed: {e}")
                    continue

            return best_result or {'trades': [], 'error': 'No successful parameter variations'}

        except Exception as e:
            logger.error(f"[ERROR] Optimized backtest failed: {e}")
            return None

    def _generate_parameter_variations(self, strategy, config: BacktestConfig) -> List:
        """Generate parameter variations for testing"""
        try:
            variations = [strategy]  # Include original

            # Create variations based on strategy type and timeframe
            if hasattr(strategy, 'parameters') and strategy.parameters:
                # Variation 1: Adjust risk parameters
                risk_variation = self._copy_strategy_with_risk_adjustment(strategy, 0.8)
                if risk_variation:
                    variations.append(risk_variation)

                # Variation 2: Adjust timeframe if different timeframes available
                if config.timeframe != '5min':
                    timeframe_variation = self._copy_strategy_with_timeframe(strategy, '5min')
                    if timeframe_variation:
                        variations.append(timeframe_variation)

                # Variation 3: Adjust entry conditions sensitivity
                sensitivity_variation = self._copy_strategy_with_sensitivity_adjustment(strategy, 1.2)
                if sensitivity_variation:
                    variations.append(sensitivity_variation)

            return variations[:3]  # Limit to 3 variations for performance

        except Exception as e:
            logger.debug(f"[BACKTEST] Parameter variation generation failed: {e}")
            return [strategy]

    def _copy_strategy_with_risk_adjustment(self, strategy, risk_factor: float):
        """Create strategy copy with adjusted risk parameters"""
        try:
            # This is a simplified version - in practice, you'd use the actual strategy copying logic
            return strategy  # Return original for now
        except Exception:
            return None

    def _copy_strategy_with_timeframe(self, strategy, new_timeframe: str):
        """Create strategy copy with different timeframe"""
        try:
            # This is a simplified version - in practice, you'd use the actual strategy copying logic
            return strategy  # Return original for now
        except Exception:
            return None

    def _copy_strategy_with_sensitivity_adjustment(self, strategy, sensitivity_factor: float):
        """Create strategy copy with adjusted sensitivity"""
        try:
            # This is a simplified version - in practice, you'd use the actual strategy copying logic
            return strategy  # Return original for now
        except Exception:
            return None

    def _calculate_quick_score(self, returns: List[float]) -> float:
        """Calculate quick performance score for parameter variation selection"""
        try:
            if not returns:
                return -100.0

            import numpy as np
            returns_array = np.array(returns)

            total_return = np.sum(returns_array)
            win_rate = np.sum(returns_array > 0) / len(returns_array)
            volatility = np.std(returns_array)

            # Simple composite score
            score = total_return * 0.4 + win_rate * 50 - volatility * 20
            return float(score)

        except Exception as e:
            logger.debug(f"[BACKTEST] Quick score calculation failed: {e}")
            return -100.0

    def _prepare_market_data_for_backtest(self, features_data: pl.DataFrame, config: BacktestConfig) -> Dict:
        """Prepare market data in the format expected by backtesting agent"""
        try:
            # Convert Polars DataFrame to format expected by backtesting agent
            market_data = features_data.to_dict(as_series=False)

            # Add metadata
            market_data['_metadata'] = {
                'timeframe': config.timeframe,
                'symbols': config.symbols,
                'start_date': config.start_date.isoformat(),
                'end_date': config.end_date.isoformat(),
                'total_rows': features_data.height
            }

            return market_data

        except Exception as e:
            logger.error(f"[ERROR] Market data preparation failed: {e}")
            return {}
    
    def _convert_strategy_config(self, strategy_config: Dict) -> Any:
        """Convert strategy config to format expected by backtesting agent"""
        try:
            # Import the StrategyConfig class
            from agents.options_strategy_evolution_agent import StrategyConfig, StrategyStatus
            
            # Create StrategyConfig object
            return StrategyConfig(
                strategy_id=strategy_config.get('strategy_id', 'test_strategy'),
                name=strategy_config.get('name', 'Test Strategy'),
                description=strategy_config.get('description', 'Strategy for backtesting'),
                parameters=strategy_config.get('parameters', {}),
                entry_conditions=strategy_config.get('entry_conditions', []),
                exit_conditions=strategy_config.get('exit_conditions', []),
                risk_management=strategy_config.get('risk_management', {}),
                market_outlook=strategy_config.get('market_outlook', 'neutral'),
                volatility_outlook=strategy_config.get('volatility_outlook', 'normal'),
                timeframe=strategy_config.get('timeframe', '5min'),
                status=StrategyStatus.EXPERIMENTAL
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to convert strategy config: {e}")
            return strategy_config
    
    async def get_available_timeframes(self) -> List[str]:
        """Get list of available timeframes from features data"""
        try:
            if not self.features_path.exists():
                return []
            
            timeframes = []
            for item in self.features_path.iterdir():
                if item.is_dir() and any(item.glob("*.parquet")):
                    timeframes.append(item.name)
            
            return sorted(timeframes)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get available timeframes: {e}")
            return []
    
    async def get_data_summary(self, timeframe: str = "5min") -> Dict[str, Any]:
        """Get summary of available data for a timeframe"""
        try:
            features_data = await self.load_features_data(timeframe)
            if features_data is None:
                return {}
            
            summary = {
                'timeframe': timeframe,
                'total_rows': features_data.height,
                'columns': features_data.columns,
                'date_range': {}
            }
            
            if 'timestamp' in features_data.columns:
                timestamps = features_data.select('timestamp').to_series()
                summary['date_range'] = {
                    'start': timestamps.min(),
                    'end': timestamps.max()
                }
            
            if 'symbol' in features_data.columns:
                symbols = features_data.select('symbol').unique().to_series().to_list()
                summary['symbols'] = symbols
            elif 'underlying' in features_data.columns:
                symbols = features_data.select('underlying').unique().to_series().to_list()
                summary['symbols'] = symbols
            
            return summary
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get data summary: {e}")
            return {}
    
    def clear_cache(self):
        """Clear cached data"""
        self.cached_data.clear()
        logger.info("[DATA] Data cache cleared")
